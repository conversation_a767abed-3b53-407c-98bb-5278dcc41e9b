@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    /* 8px Design Scale System - Border Radius */
    --radius-xs: 0.25rem; /* 4px */
    --radius-sm: 0.5rem;  /* 8px */
    --radius-md: 0.75rem; /* 12px */
    --radius-lg: 1rem;    /* 16px */
    --radius-xl: 1.5rem;  /* 24px */
    --radius-2xl: 2rem;   /* 32px */
    --radius: var(--radius-sm); /* Default to 8px */

    /* 8px Design Scale System - Spacing */
    --spacing-0: 0;
    --spacing-1: 0.125rem; /* 2px */
    --spacing-2: 0.25rem;  /* 4px */
    --spacing-3: 0.5rem;   /* 8px */
    --spacing-4: 0.75rem;  /* 12px */
    --spacing-5: 1rem;     /* 16px */
    --spacing-6: 1.25rem;  /* 20px */
    --spacing-7: 1.5rem;   /* 24px */
    --spacing-8: 2rem;     /* 32px */
    --spacing-9: 2.5rem;   /* 40px */
    --spacing-10: 3rem;    /* 48px */
    --spacing-12: 4rem;    /* 64px */
    --spacing-16: 5rem;    /* 80px */
    --spacing-20: 6rem;    /* 96px */
    --spacing-24: 8rem;    /* 128px */

    /* Typography Scale based on 8px system */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */
    --font-size-5xl: 3rem;     /* 48px */
    --font-size-6xl: 3.75rem;  /* 60px */

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    /* Light Mode - Brand Colors */
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);

    /* Primary Brand Color: #3B82F6 converted to OKLCH */
    --primary: oklch(0.6398 0.1427 263.89);
    --primary-foreground: oklch(1 0 0);

    /* Secondary colors based on brand palette */
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.145 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.145 0 0);

    /* Status colors */
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(1 0 0);
    --success: oklch(0.6 0.15 145);
    --success-foreground: oklch(1 0 0);
    --warning: oklch(0.7 0.15 85);
    --warning-foreground: oklch(0.145 0 0);
    --info: oklch(0.6398 0.1427 263.89);
    --info-foreground: oklch(1 0 0);

    /* UI colors */
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.6398 0.1427 263.89);

    /* Chart colors with brand-aligned palette */
    --chart-1: oklch(0.6398 0.1427 263.89);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* Sidebar colors */
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.6398 0.1427 263.89);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.145 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.6398 0.1427 263.89);

    /* Gender-specific colors */
    --gender-putra: oklch(0.6398 0.1427 263.89);
    --gender-putra-foreground: oklch(1 0 0);
    --gender-putri: oklch(0.7 0.15 330);
    --gender-putri-foreground: oklch(1 0 0);
    --gender-campur: oklch(0.65 0.15 290);
    --gender-campur-foreground: oklch(1 0 0);
}

.dark {
    /* Dark Mode - Brand Colors */
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);

    /* Primary Brand Color: #2563EB converted to OKLCH for dark mode */
    --primary: oklch(0.5756 0.1727 263.89);
    --primary-foreground: oklch(1 0 0);

    /* Secondary colors for dark mode */
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);

    /* Status colors for dark mode */
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.985 0 0);
    --success: oklch(0.5 0.12 145);
    --success-foreground: oklch(0.985 0 0);
    --warning: oklch(0.6 0.12 85);
    --warning-foreground: oklch(0.145 0 0);
    --info: oklch(0.5756 0.1727 263.89);
    --info-foreground: oklch(1 0 0);

    /* UI colors for dark mode */
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.5756 0.1727 263.89);

    /* Chart colors for dark mode */
    --chart-1: oklch(0.5756 0.1727 263.89);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    /* Sidebar colors for dark mode */
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.5756 0.1727 263.89);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.5756 0.1727 263.89);

    /* Gender-specific colors for dark mode */
    --gender-putra: oklch(0.5756 0.1727 263.89);
    --gender-putra-foreground: oklch(1 0 0);
    --gender-putri: oklch(0.6 0.12 330);
    --gender-putri-foreground: oklch(1 0 0);
    --gender-campur: oklch(0.55 0.12 290);
    --gender-campur-foreground: oklch(1 0 0);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}
