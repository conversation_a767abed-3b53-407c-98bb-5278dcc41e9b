import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn, getGenderColors, getStatusColors, spacingClasses, radiusClasses } from '@/lib/design-system';
import { Kost } from '@/types';
import {
    MapPin,
    Users,
    Calendar,
    Bed,
    Eye,
    MessageCircle,
    Star,
    Wifi,
    Car,
    Shield,
    Bath
} from 'lucide-react';
import React from 'react';

interface KostCardProps {
    kost: Kost;
    onView?: (kost: Kost) => void;
    onInquiry?: (kost: Kost) => void;
    showOwnerInfo?: boolean;
    className?: string;
}

const KostCard: React.FC<KostCardProps> = ({
    kost,
    onView,
    onInquiry,
    showOwnerInfo = false,
    className = '',
}) => {
    const coverImage = kost.images?.find(img => img.image_type === 'cover') || kost.images?.[0];
    
    // Removed hardcoded color functions - now using design system utilities

    const getFacilityIcon = (category: string) => {
        switch (category) {
            case 'kamar': return <Bed className="h-4 w-4" />;
            case 'kamar_mandi': return <Bath className="h-4 w-4" />;
            case 'umum': return <Wifi className="h-4 w-4" />;
            case 'keamanan': return <Shield className="h-4 w-4" />;
            case 'parkir': return <Car className="h-4 w-4" />;
            default: return <Star className="h-4 w-4" />;
        }
    };

    const statusColors = getStatusColors(kost.status as 'approved' | 'pending' | 'rejected' | 'draft');

    return (
        <Card className={cn(
            "overflow-hidden transition-all duration-200 hover:shadow-lg",
            radiusClasses.lg,
            className
        )}>
            {/* Cover Image */}
            <div className="relative h-48 overflow-hidden">
                {coverImage ? (
                    <img
                        src={coverImage.image_url}
                        alt={coverImage.alt_text || kost.name}
                        className="h-full w-full object-cover transition-transform duration-200 hover:scale-105"
                    />
                ) : (
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                        <Bed className="h-12 w-12 text-muted-foreground" />
                    </div>
                )}

                {/* Status Badge */}
                <div className={cn("absolute", spacingClasses.gap[3])}>
                    <Badge className={cn(
                        statusColors.bg,
                        statusColors.text,
                        statusColors.hover,
                        radiusClasses.sm
                    )}>
                        {kost.status === 'approved' ? 'Tersedia' :
                         kost.status === 'pending' ? 'Menunggu' :
                         kost.status === 'rejected' ? 'Ditolak' : 'Draft'}
                    </Badge>
                </div>

                {/* Available Rooms Badge */}
                {kost.available_rooms > 0 && (
                    <div className={cn("absolute top-2 left-2")}>
                        <Badge
                            variant="secondary"
                            className={cn(
                                "bg-white/90 text-gray-800 dark:bg-gray-800/90 dark:text-gray-200",
                                radiusClasses.sm
                            )}
                        >
                            {kost.available_rooms} kamar tersisa
                        </Badge>
                    </div>
                )}
            </div>

            <CardHeader className={cn(spacingClasses.p[4], "pb-3")}>
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg leading-tight line-clamp-2">
                            {kost.name}
                        </h3>
                        <div className={cn(
                            "flex items-center mt-1 text-sm text-muted-foreground",
                            spacingClasses.gap[2]
                        )}>
                            <MapPin className="h-4 w-4" />
                            <span className="line-clamp-1">{kost.city}, {kost.province}</span>
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                            {kost.formatted_price}
                        </div>
                        <div className="text-xs text-muted-foreground">per bulan</div>
                    </div>
                </div>
            </CardHeader>

            <CardContent className={cn(spacingClasses.p[4], "space-y-3")}>
                {/* Gender and Type Badges */}
                <div className={cn("flex", spacingClasses.gap[3])}>
                    <Badge className={cn(
                        getGenderColors(kost.gender_type as 'putra' | 'putri' | 'campur').bg,
                        getGenderColors(kost.gender_type as 'putra' | 'putri' | 'campur').text,
                        getGenderColors(kost.gender_type as 'putra' | 'putri' | 'campur').hover,
                        radiusClasses.sm,
                        "flex items-center",
                        spacingClasses.gap[2]
                    )}>
                        <Users className="h-3 w-3" />
                        {kost.gender_type === 'putra' ? 'Putra' :
                         kost.gender_type === 'putri' ? 'Putri' : 'Campur'}
                    </Badge>
                    <Badge variant="outline" className={cn(
                        radiusClasses.sm,
                        "flex items-center",
                        spacingClasses.gap[2]
                    )}>
                        <Calendar className="h-3 w-3" />
                        {kost.kost_type === 'bulanan' ? 'Bulanan' :
                         kost.kost_type === 'harian' ? 'Harian' : 'Bulanan/Harian'}
                    </Badge>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-2">
                    {kost.description}
                </p>

                {/* Facilities */}
                {kost.facilities && kost.facilities.length > 0 && (
                    <div className={cn("space-y-2")}>
                        <Separator />
                        <div className={cn("flex flex-wrap", spacingClasses.gap[3])}>
                            {kost.facilities.slice(0, 4).map((facility) => (
                                <div
                                    key={facility.id}
                                    className={cn(
                                        "flex items-center text-xs text-muted-foreground",
                                        spacingClasses.gap[2]
                                    )}
                                >
                                    {getFacilityIcon(facility.category)}
                                    <span>{facility.name}</span>
                                </div>
                            ))}
                            {kost.facilities.length > 4 && (
                                <span className="text-xs text-muted-foreground">
                                    +{kost.facilities.length - 4} lainnya
                                </span>
                            )}
                        </div>
                    </div>
                )}

                {/* Owner Info */}
                {showOwnerInfo && kost.owner && (
                    <div className="space-y-2">
                        <Separator />
                        <div className={cn(
                            "flex items-center text-sm",
                            spacingClasses.gap[3]
                        )}>
                            <div className={cn(
                                "h-6 w-6 bg-primary/10 flex items-center justify-center",
                                radiusClasses.full
                            )}>
                                <span className="text-xs font-medium text-primary">
                                    {kost.owner.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <span className="text-muted-foreground">
                                Pemilik: {kost.owner.name}
                            </span>
                        </div>
                    </div>
                )}
            </CardContent>

            <CardFooter className={cn(
                spacingClasses.p[4],
                "pt-3",
                spacingClasses.gap[3]
            )}>
                <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                        "flex-1 flex items-center",
                        spacingClasses.gap[3],
                        radiusClasses.sm
                    )}
                    onClick={() => onView?.(kost)}
                >
                    <Eye className="h-4 w-4" />
                    Lihat Detail
                </Button>
                {kost.status === 'approved' && kost.available_rooms > 0 && (
                    <Button
                        size="sm"
                        className={cn(
                            "flex-1 flex items-center",
                            spacingClasses.gap[3],
                            radiusClasses.sm
                        )}
                        onClick={() => onInquiry?.(kost)}
                    >
                        <MessageCircle className="h-4 w-4" />
                        Tanya Kost
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
};

export default KostCard;
