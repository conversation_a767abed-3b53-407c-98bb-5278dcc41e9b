/**
 * Design System Utilities
 * Provides consistent color, spacing, and styling utilities based on the 8px design scale
 */

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

/**
 * 8px Design Scale Spacing System
 * All spacing values are based on 8px increments
 */
export const spacing = {
    0: '0',
    1: '0.125rem', // 2px
    2: '0.25rem',  // 4px
    3: '0.5rem',   // 8px
    4: '0.75rem',  // 12px
    5: '1rem',     // 16px
    6: '1.25rem',  // 20px
    7: '1.5rem',   // 24px
    8: '2rem',     // 32px
    9: '2.5rem',   // 40px
    10: '3rem',    // 48px
    12: '4rem',    // 64px
    16: '5rem',    // 80px
    20: '6rem',    // 96px
    24: '8rem',    // 128px
} as const;

/**
 * Border Radius System based on 8px scale
 */
export const radius = {
    xs: '0.25rem', // 4px
    sm: '0.5rem',  // 8px
    md: '0.75rem', // 12px
    lg: '1rem',    // 16px
    xl: '1.5rem',  // 24px
    '2xl': '2rem', // 32px
    full: '9999px',
} as const;

/**
 * Typography Scale based on 8px system
 */
export const typography = {
    xs: 'text-xs',     // 12px
    sm: 'text-sm',     // 14px
    base: 'text-base', // 16px
    lg: 'text-lg',     // 18px
    xl: 'text-xl',     // 20px
    '2xl': 'text-2xl', // 24px
    '3xl': 'text-3xl', // 30px
    '4xl': 'text-4xl', // 36px
    '5xl': 'text-5xl', // 48px
    '6xl': 'text-6xl', // 60px
} as const;

/**
 * Gender-specific color utilities
 */
export const genderColors = {
    putra: {
        bg: 'bg-blue-100 dark:bg-blue-900/20',
        text: 'text-blue-800 dark:text-blue-200',
        border: 'border-blue-200 dark:border-blue-800',
        hover: 'hover:bg-blue-200 dark:hover:bg-blue-900/30',
    },
    putri: {
        bg: 'bg-pink-100 dark:bg-pink-900/20',
        text: 'text-pink-800 dark:text-pink-200',
        border: 'border-pink-200 dark:border-pink-800',
        hover: 'hover:bg-pink-200 dark:hover:bg-pink-900/30',
    },
    campur: {
        bg: 'bg-purple-100 dark:bg-purple-900/20',
        text: 'text-purple-800 dark:text-purple-200',
        border: 'border-purple-200 dark:border-purple-800',
        hover: 'hover:bg-purple-200 dark:hover:bg-purple-900/30',
    },
} as const;

/**
 * Status color utilities
 */
export const statusColors = {
    approved: {
        bg: 'bg-green-100 dark:bg-green-900/20',
        text: 'text-green-800 dark:text-green-200',
        border: 'border-green-200 dark:border-green-800',
        hover: 'hover:bg-green-200 dark:hover:bg-green-900/30',
    },
    pending: {
        bg: 'bg-yellow-100 dark:bg-yellow-900/20',
        text: 'text-yellow-800 dark:text-yellow-200',
        border: 'border-yellow-200 dark:border-yellow-800',
        hover: 'hover:bg-yellow-200 dark:hover:bg-yellow-900/30',
    },
    rejected: {
        bg: 'bg-red-100 dark:bg-red-900/20',
        text: 'text-red-800 dark:text-red-200',
        border: 'border-red-200 dark:border-red-800',
        hover: 'hover:bg-red-200 dark:hover:bg-red-900/30',
    },
    draft: {
        bg: 'bg-gray-100 dark:bg-gray-900/20',
        text: 'text-gray-800 dark:text-gray-200',
        border: 'border-gray-200 dark:border-gray-800',
        hover: 'hover:bg-gray-200 dark:hover:bg-gray-900/30',
    },
} as const;

/**
 * Get gender-specific color classes
 */
export function getGenderColors(gender: 'putra' | 'putri' | 'campur') {
    return genderColors[gender] || genderColors.campur;
}

/**
 * Get status-specific color classes
 */
export function getStatusColors(status: 'approved' | 'pending' | 'rejected' | 'draft') {
    return statusColors[status] || statusColors.draft;
}

/**
 * Consistent spacing utilities using 8px scale
 */
export const spacingClasses = {
    // Padding
    p: {
        0: 'p-0',
        1: 'p-0.5',  // 2px
        2: 'p-1',    // 4px
        3: 'p-2',    // 8px
        4: 'p-3',    // 12px
        5: 'p-4',    // 16px
        6: 'p-5',    // 20px
        7: 'p-6',    // 24px
        8: 'p-8',    // 32px
        9: 'p-10',   // 40px
        10: 'p-12',  // 48px
        12: 'p-16',  // 64px
        16: 'p-20',  // 80px
        20: 'p-24',  // 96px
        24: 'p-32',  // 128px
    },
    // Margin
    m: {
        0: 'm-0',
        1: 'm-0.5',  // 2px
        2: 'm-1',    // 4px
        3: 'm-2',    // 8px
        4: 'm-3',    // 12px
        5: 'm-4',    // 16px
        6: 'm-5',    // 20px
        7: 'm-6',    // 24px
        8: 'm-8',    // 32px
        9: 'm-10',   // 40px
        10: 'm-12',  // 48px
        12: 'm-16',  // 64px
        16: 'm-20',  // 80px
        20: 'm-24',  // 96px
        24: 'm-32',  // 128px
    },
    // Gap
    gap: {
        0: 'gap-0',
        1: 'gap-0.5',  // 2px
        2: 'gap-1',    // 4px
        3: 'gap-2',    // 8px
        4: 'gap-3',    // 12px
        5: 'gap-4',    // 16px
        6: 'gap-5',    // 20px
        7: 'gap-6',    // 24px
        8: 'gap-8',    // 32px
        9: 'gap-10',   // 40px
        10: 'gap-12',  // 48px
        12: 'gap-16',  // 64px
        16: 'gap-20',  // 80px
        20: 'gap-24',  // 96px
        24: 'gap-32',  // 128px
    },
} as const;

/**
 * Consistent border radius utilities
 */
export const radiusClasses = {
    xs: 'rounded-sm',    // 4px
    sm: 'rounded',       // 8px
    md: 'rounded-md',    // 12px
    lg: 'rounded-lg',    // 16px
    xl: 'rounded-xl',    // 24px
    '2xl': 'rounded-2xl', // 32px
    full: 'rounded-full',
} as const;

/**
 * Brand color utilities
 */
export const brandColors = {
    primary: 'text-primary',
    primaryBg: 'bg-primary',
    primaryHover: 'hover:bg-primary/90',
    primaryForeground: 'text-primary-foreground',
} as const;
