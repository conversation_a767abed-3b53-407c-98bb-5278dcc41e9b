import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn, spacingClasses, radiusClasses } from '@/lib/design-system';
import { type SharedData } from '@/types';
import { Head, Link, usePage, router } from '@inertiajs/react';
import { Search, Home, Shield, Clock } from 'lucide-react';
import { useState } from 'react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState('');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            // If user is authenticated, go to search page with query
            if (auth.user) {
                router.visit(route('pencari.search'), {
                    method: 'get',
                    data: { search: searchQuery.trim() }
                });
            } else {
                // If not authenticated, redirect to login first
                router.visit(route('login'));
            }
        }
    };

    return (
        <>
            <Head title="SIM Kost - Sistem Informasi Manajemen Kost">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-primary/5 to-primary/10 dark:from-gray-900 dark:to-gray-800">
                {/* Navigation */}
                <nav className="bg-white/80 backdrop-blur-md border-b border-border dark:bg-gray-900/80">
                    <div className={cn(
                        "max-w-7xl mx-auto",
                        spacingClasses.p[5],
                        "sm:px-6 lg:px-8"
                    )}>
                        <div className="flex justify-between items-center h-16">
                            <div className={cn("flex items-center", spacingClasses.gap[3])}>
                                <Home className="h-8 w-8 text-primary" />
                                <span className="text-xl font-bold text-foreground">SIM Kost</span>
                            </div>
                            <div className={cn("flex items-center", spacingClasses.gap[5])}>
                                {auth.user ? (
                                    <Link href={route('dashboard')}>
                                        <Button className={cn(
                                            radiusClasses.sm,
                                            "transition-colors"
                                        )}>
                                            Dashboard
                                        </Button>
                                    </Link>
                                ) : (
                                    <>
                                        <Link
                                            href={route('login')}
                                            className="text-muted-foreground hover:text-primary transition-colors"
                                        >
                                            Masuk
                                        </Link>
                                        <Link href={route('register')}>
                                            <Button className={cn(
                                                radiusClasses.sm,
                                                "transition-colors"
                                            )}>
                                                Daftar
                                            </Button>
                                        </Link>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <div className={cn(
                    "max-w-7xl mx-auto",
                    spacingClasses.p[5],
                    "sm:px-6 lg:px-8",
                    spacingClasses.p[20]
                )}>
                    <div className="text-center">
                        <h1 className={cn(
                            "text-4xl md:text-6xl font-bold text-foreground",
                            spacingClasses.m[7]
                        )}>
                            Temukan <span className="text-primary">Kost Impian</span> Anda
                        </h1>
                        <p className={cn(
                            "text-xl text-muted-foreground max-w-3xl mx-auto",
                            spacingClasses.m[10]
                        )}>
                            Platform terpercaya untuk mencari dan mengelola kost dengan mudah.
                            Dilengkapi dengan AI search untuk pengalaman yang lebih personal.
                        </p>

                        {/* Search Bar */}
                        <div className={cn(
                            "max-w-2xl mx-auto",
                            spacingClasses.m[12]
                        )}>
                            <form onSubmit={handleSearch} className="relative">
                                <Search className={cn(
                                    "absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"
                                )} />
                                <Input
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    placeholder="Cari kost berdasarkan lokasi, fasilitas, atau harga..."
                                    className={cn(
                                        "pl-12 pr-4 py-4 text-lg border-2 focus:border-primary",
                                        radiusClasses.xl
                                    )}
                                />
                                <Button
                                    type="submit"
                                    className={cn(
                                        "absolute right-2 top-1/2 transform -translate-y-1/2",
                                        radiusClasses.sm
                                    )}
                                >
                                    Cari
                                </Button>
                            </form>
                        </div>

                        {/* Quick Stats */}
                        <div className={cn(
                            "grid grid-cols-1 md:grid-cols-3",
                            spacingClasses.gap[10],
                            spacingClasses.m[16]
                        )}>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-primary">1000+</div>
                                <div className="text-muted-foreground">Kost Tersedia</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-primary">50+</div>
                                <div className="text-muted-foreground">Kota</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-primary">5000+</div>
                                <div className="text-muted-foreground">Pengguna Aktif</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Features Section */}
                <div className={cn(
                    "bg-card",
                    spacingClasses.p[20]
                )}>
                    <div className={cn(
                        "max-w-7xl mx-auto",
                        spacingClasses.p[5],
                        "sm:px-6 lg:px-8"
                    )}>
                        <div className={cn(
                            "text-center",
                            spacingClasses.m[16]
                        )}>
                            <h2 className={cn(
                                "text-3xl md:text-4xl font-bold text-foreground",
                                spacingClasses.m[5]
                            )}>
                                Mengapa Memilih SIM Kost?
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                                Platform yang dirancang khusus untuk memudahkan pencarian dan pengelolaan kost
                            </p>
                        </div>

                        <div className={cn(
                            "grid grid-cols-1 md:grid-cols-3",
                            spacingClasses.gap[10]
                        )}>
                            <Card className={cn(
                                "text-center hover:shadow-lg transition-shadow",
                                spacingClasses.p[7],
                                radiusClasses.lg
                            )}>
                                <CardHeader>
                                    <Search className={cn(
                                        "h-12 w-12 text-primary mx-auto",
                                        spacingClasses.m[5]
                                    )} />
                                    <CardTitle>AI-Powered Search</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Cari kost dengan bahasa natural. AI kami akan memahami kebutuhan Anda dan memberikan rekomendasi terbaik.
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className={cn(
                                "text-center hover:shadow-lg transition-shadow",
                                spacingClasses.p[7],
                                radiusClasses.lg
                            )}>
                                <CardHeader>
                                    <Shield className={cn(
                                        "h-12 w-12 text-primary mx-auto",
                                        spacingClasses.m[5]
                                    )} />
                                    <CardTitle>Terpercaya & Aman</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Semua kost telah diverifikasi. Sistem keamanan berlapis untuk melindungi data dan transaksi Anda.
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className={cn(
                                "text-center hover:shadow-lg transition-shadow",
                                spacingClasses.p[7],
                                radiusClasses.lg
                            )}>
                                <CardHeader>
                                    <Clock className={cn(
                                        "h-12 w-12 text-primary mx-auto",
                                        spacingClasses.m[5]
                                    )} />
                                    <CardTitle>Real-time Updates</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>
                                        Notifikasi real-time untuk inquiry, update ketersediaan kamar, dan komunikasi dengan pemilik kost.
                                    </CardDescription>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>

                {/* CTA Section */}
                <div className={cn(
                    "bg-primary",
                    spacingClasses.p[20]
                )}>
                    <div className={cn(
                        "max-w-4xl mx-auto text-center",
                        spacingClasses.p[5],
                        "sm:px-6 lg:px-8"
                    )}>
                        <h2 className={cn(
                            "text-3xl md:text-4xl font-bold text-primary-foreground",
                            spacingClasses.m[7]
                        )}>
                            Siap Menemukan Kost Impian Anda?
                        </h2>
                        <p className={cn(
                            "text-xl text-primary-foreground/80",
                            spacingClasses.m[10]
                        )}>
                            Bergabunglah dengan ribuan pengguna yang telah menemukan kost terbaik melalui platform kami
                        </p>
                        <div className={cn(
                            "flex flex-col sm:flex-row justify-center",
                            spacingClasses.gap[5]
                        )}>
                            {!auth.user && (
                                <>
                                    <Link href={route('register')}>
                                        <Button
                                            size="lg"
                                            variant="secondary"
                                            className={cn(
                                                "w-full sm:w-auto",
                                                radiusClasses.sm
                                            )}
                                        >
                                            Daftar Sebagai Pencari Kost
                                        </Button>
                                    </Link>
                                    <Link href={route('register')}>
                                        <Button
                                            size="lg"
                                            variant="outline"
                                            className={cn(
                                                "w-full sm:w-auto text-primary-foreground border-primary-foreground hover:bg-primary-foreground hover:text-primary",
                                                radiusClasses.sm
                                            )}
                                        >
                                            Daftar Sebagai Pemilik Kost
                                        </Button>
                                    </Link>
                                </>
                            )}
                            {auth.user && (
                                <Link href={route('dashboard')}>
                                    <Button
                                        size="lg"
                                        variant="secondary"
                                        className={cn(
                                            "w-full sm:w-auto",
                                            radiusClasses.sm
                                        )}
                                    >
                                        Masuk ke Dashboard
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <footer className={cn(
                    "bg-gray-900 text-white",
                    spacingClasses.p[12]
                )}>
                    <div className={cn(
                        "max-w-7xl mx-auto",
                        spacingClasses.p[5],
                        "sm:px-6 lg:px-8"
                    )}>
                        <div className={cn(
                            "grid grid-cols-1 md:grid-cols-4",
                            spacingClasses.gap[10]
                        )}>
                            <div>
                                <div className={cn(
                                    "flex items-center",
                                    spacingClasses.m[5],
                                    spacingClasses.gap[3]
                                )}>
                                    <Home className="h-8 w-8 text-primary" />
                                    <span className="text-xl font-bold">SIM Kost</span>
                                </div>
                                <p className="text-gray-400">
                                    Platform terpercaya untuk mencari dan mengelola kost dengan mudah.
                                </p>
                            </div>
                            <div>
                                <h3 className={cn(
                                    "font-semibold",
                                    spacingClasses.m[5]
                                )}>Untuk Pencari</h3>
                                <ul className={cn(
                                    "space-y-2 text-gray-400"
                                )}>
                                    <li>Cari Kost</li>
                                    <li>AI Search</li>
                                    <li>Favorit</li>
                                    <li>Riwayat Inquiry</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className={cn(
                                    "font-semibold",
                                    spacingClasses.m[5]
                                )}>Untuk Pemilik</h3>
                                <ul className={cn(
                                    "space-y-2 text-gray-400"
                                )}>
                                    <li>Kelola Kost</li>
                                    <li>Manajemen Inquiry</li>
                                    <li>Statistik</li>
                                    <li>Promosi</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className={cn(
                                    "font-semibold",
                                    spacingClasses.m[5]
                                )}>Bantuan</h3>
                                <ul className={cn(
                                    "space-y-2 text-gray-400"
                                )}>
                                    <li>FAQ</li>
                                    <li>Kontak</li>
                                    <li>Panduan</li>
                                    <li>Kebijakan</li>
                                </ul>
                            </div>
                        </div>
                        <div className={cn(
                            "border-t border-gray-800 text-center text-gray-400",
                            spacingClasses.m[10],
                            spacingClasses.p[10]
                        )}>
                            <p>&copy; 2025 SIM Kost. All rights reserved.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
